
# from .get_db import db
from .resolve_llm import resolve_llm

from .track_time import time_it

# @time_it
# def sentiment_classification(data):
#     latest_message_str = data["latest_message_str"]
#     try:
#         prompt_db = db["prompt"] # variable naming bhayena
#         sentiment_classification_prompt = prompt_db.find_one({"name":"sentiment_classification"}) # k ho yo jepayetei naming?
#         prompt_ = sentiment_classification_prompt["text"]
#         model = sentiment_classification_prompt["model"]
#         llm= resolve_llm(model_name=model)
#         sentiment_prompt=prompt_.format(question=latest_message_str)
#         response = llm.complete(prompt=sentiment_prompt,formatted=True)
#         return response.text.strip(" ").split(":")[-1]
#     except Exception as e:
#         # loggers.error(f"Error classifying sentiment: {e}") # i dont see the point of this error log
#         raise e


@time_it
def detect_language(data,current_user):
    latest_message_str = data["latest_message_str"]
    language_prompt = data["prompt"]
    try:
        from pydantic import BaseModel
        class languageidentifier(BaseModel):
            language:str
        # prompt_db = db["prompt"]
        # language_prompt = prompt_db.find_one({"name":"language_detection"})
        prompt_ = language_prompt["text"]
        model = language_prompt["model"]
        llm= resolve_llm(model_name=model,current_user=current_user)
        language_prompt=prompt_.format(question=latest_message_str)
        # response = llm.complete(prompt=language_prompt,formatted=True)
        response=llm.complete(prompt=language_prompt,formatted=True)
        try:
            cleaned_text = response.text.split(":", 1)[-1].strip()
        except Exception as e:
            print(f"Error: {e}")
            cleaned_text = response.text  # fallback to raw text
        usage=response.raw.usage.model_dump()
        return cleaned_text,{"model":model,**usage}
    except Exception as e:
        # loggers.error(f"Error detecting language: {e}") # same here fix this or remove this
        raise e
