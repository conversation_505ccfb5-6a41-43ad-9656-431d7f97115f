import time
import functools

def time_it(func):
    """Decorator to measure and print execution time of a function."""
    # @functools.wraps(func)
    # def wrapper(*args, **kwargs):
    #     start_time = time.perf_counter()  # High-precision timer
    #     result = func(*args, **kwargs)
    #     end_time = time.perf_counter()
    #     print(f"**{func.__name__} executed in {end_time - start_time:.6f} seconds**")
    #     return result
    return True