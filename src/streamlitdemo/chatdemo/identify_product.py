from .resolve_llm import resolve_llm

# from .get_db import db
from typing import Any
from .track_time import time_it

@time_it
def identify_product(data,current_user: Any = None) -> str:
    """Uses Summary and Latest user message to identify the product"""

    latest_message_str = data["latest_message_str"]
    # summary_str = data["summary_str"]
    identify_prod_prompt = data["prompt"]
    try:
        prompt_ = identify_prod_prompt["text"]
        model = identify_prod_prompt["model"]
        llm= resolve_llm(model_name=model,current_user=current_user)
        identify_prod_prompt=prompt_.format(query=latest_message_str)
        response = llm.complete(prompt=identify_prod_prompt,formatted=True)
        print(response.raw)
        usage=response.raw.usage.model_dump()
        text=response.text.strip(" ").split(":")[-1]
        try:
            if "No Products Found" in text:
                return None,None
            elif "No Product Found" in text:
                return None,None
            else:
                return text,None
        except:
            return text,{
                "model":model,**usage}
    except Exception as e:
        # loggers.error(f"Error detecting language: {e}") # same here fix this or remove this
        raise e

